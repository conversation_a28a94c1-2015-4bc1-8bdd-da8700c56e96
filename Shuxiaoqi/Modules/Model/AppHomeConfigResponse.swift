//
//  AppHomeConfigResponse.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/7/11.
//

import Foundation
import SmartCodable

struct AppHomeConfigResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String?
    var msg: String?
    var data: [AppHomeConfigResponseData]?

    // 计算属性：判断请求是否成功
    var isSuccess: Bool {
        return status == 200
    }

    // 计算属性：获取用于显示的消息
    public var displayMessage: String {
        if let message = msg, !message.isEmpty {
            return message
        }
        if let errorMsg = errMsg, !errorMsg.isEmpty {
            return errorMsg
        }
        return isSuccess ? "成功" : "未知错误"
    }
}

// MARK: - ToAddress解析结构
struct ToAddressConfig: SmartCodable {
    var type: String = ""
    var url: String = ""
}

// MARK: - AppHomeConfigResponseData
struct AppHomeConfigResponseData: SmartCodable {
    var id: Int?
    var sort: Int?
    var icon: String?
    var toAddress: String?
    var label: String?
    
    // 解析toAddress字段
    var parsedToAddress: ToAddressConfig? {
        guard let toAddressString = toAddress else { return nil }
        
        do {
            let data = toAddressString.data(using: .utf8)
            let config = try JSONDecoder().decode(ToAddressConfig.self, from: data ?? Data())
            return config
        } catch {
            print("解析toAddress失败: \(error)")
            return nil
        }
    }
    
    // 获取跳转URL
    var jumpUrl: String {
        return parsedToAddress?.url ?? ""
    }
    
    // 获取跳转类型
    var jumpType: String {
        return parsedToAddress?.type ?? "webView"
    }
}
